#!/usr/bin/env python3
"""
测试DatetimeIndex访问修复和2025年7月22日参考日期
"""

import pandas as pd
import numpy as np

def test_datetime_index_access():
    """测试DatetimeIndex的正确访问方法"""
    print("=" * 60)
    print("测试DatetimeIndex访问修复")
    print("=" * 60)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', '2024-12-31', freq='B')  # 工作日
    target_date = pd.Timestamp('2024-07-22')
    
    print(f"可用日期数量: {len(dates)}")
    print(f"目标日期: {target_date}")
    print(f"日期索引类型: {type(dates)}")
    
    # 计算差异
    differences = abs(dates - target_date)
    min_diff_idx = differences.argmin()
    
    print(f"最小差异索引: {min_diff_idx}")
    print(f"最小差异索引类型: {type(min_diff_idx)}")
    
    # 测试不同的访问方法
    print("\n测试访问方法:")
    
    # 方法1: 使用.iloc（可能失败）
    try:
        result_iloc = dates.iloc[min_diff_idx]
        print(f"✅ 方法1 - dates.iloc[idx]: {result_iloc}")
    except Exception as e:
        print(f"❌ 方法1失败 - dates.iloc[idx]: {e}")
    
    # 方法2: 直接索引访问（推荐）
    try:
        result_direct = dates[min_diff_idx]
        print(f"✅ 方法2 - dates[idx]: {result_direct}")
        return True
    except Exception as e:
        print(f"❌ 方法2失败 - dates[idx]: {e}")
    
    # 方法3: 使用.values数组访问
    try:
        result_values = dates.values[min_diff_idx]
        result_timestamp = pd.Timestamp(result_values)
        print(f"✅ 方法3 - dates.values[idx]: {result_timestamp}")
        return True
    except Exception as e:
        print(f"❌ 方法3失败 - dates.values[idx]: {e}")
    
    return False

def test_reference_date_logic():
    """测试2025年7月22日参考日期逻辑"""
    print("\n" + "=" * 60)
    print("测试2025年7月22日参考日期逻辑")
    print("=" * 60)
    
    # 模拟参考日期
    reference_date = pd.Timestamp('2025-07-22')
    print(f"参考日期: {reference_date}")
    
    # 获取参考日期的月日
    reference_month_day = (reference_date.month, reference_date.day)
    print(f"参考月日: {reference_month_day}")
    
    # 计算要分析的历史年份（过去5年）
    current_year = reference_date.year
    analysis_years = list(range(current_year-5, current_year))
    print(f"分析历史年份: {analysis_years}")
    
    # 对每个历史年份，创建对应的同期日期
    print("\n历史同期日期:")
    for year in analysis_years:
        historical_date = pd.Timestamp(year=year, month=reference_month_day[0], day=reference_month_day[1])
        window_end_30 = historical_date + pd.Timedelta(days=30)
        window_end_60 = historical_date + pd.Timedelta(days=60)
        window_end_90 = historical_date + pd.Timedelta(days=90)
        
        print(f"  {year}年: {historical_date.strftime('%Y-%m-%d')} 到 "
              f"{window_end_30.strftime('%m-%d')} / "
              f"{window_end_60.strftime('%m-%d')} / "
              f"{window_end_90.strftime('%m-%d')} (30/60/90天)")
    
    print(f"\n✅ 参考日期逻辑正确，将分析过去5年同期数据")
    return True

def simulate_fixed_find_closest_trading_day():
    """模拟修复后的_find_closest_trading_day方法"""
    print("\n" + "=" * 60)
    print("模拟修复后的_find_closest_trading_day方法")
    print("=" * 60)
    
    def find_closest_trading_day_fixed(filtered_available_dates, target_date):
        """模拟修复后的方法"""
        if not filtered_available_dates:
            return None
            
        available_dates_ts = pd.to_datetime(filtered_available_dates)
        
        # 找到最接近的日期
        differences = abs(available_dates_ts - target_date)
        min_diff_idx = differences.argmin()
        
        # 获取最小差异的天数（修复numpy.timedelta64问题）
        try:
            differences_array = differences.values
            min_diff_timedelta = differences_array[min_diff_idx]
            min_diff_days = int(min_diff_timedelta / pd.Timedelta(days=1))
        except Exception:
            try:
                min_diff_timedelta = pd.Timedelta(differences.values[min_diff_idx])
                min_diff_days = min_diff_timedelta.days
            except Exception:
                min_diff_timedelta = differences.values[min_diff_idx]
                min_diff_days = int(min_diff_timedelta / np.timedelta64(1, 'D'))
        
        # 如果差异超过7天，认为没有合适的匹配
        if min_diff_days > 7:
            return None
            
        # 修复：使用直接索引访问而不是.iloc
        return available_dates_ts[min_diff_idx]
    
    # 测试数据
    available_dates = pd.date_range('2020-01-01', '2025-12-31', freq='B')  # 工作日
    test_cases = [
        pd.Timestamp('2025-07-22'),  # 参考日期
        pd.Timestamp('2024-07-22'),  # 历史同期
        pd.Timestamp('2023-07-22'),  # 历史同期
        pd.Timestamp('2022-07-22'),  # 历史同期
    ]
    
    print("测试用例:")
    success_count = 0
    for i, target_date in enumerate(test_cases, 1):
        try:
            result = find_closest_trading_day_fixed(available_dates, target_date)
            if result is not None:
                diff_days = abs((result - target_date).days)
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: {result.strftime('%Y-%m-%d')}, "
                      f"差异: {diff_days}天 ✅")
                success_count += 1
            else:
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: None (差异超过7天) ⚠️")
                success_count += 1
        except Exception as e:
            print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                  f"错误: {e} ❌")
            return False
    
    print(f"\n✅ 所有测试用例通过 ({success_count}/{len(test_cases)})")
    return True

if __name__ == "__main__":
    print("开始测试DatetimeIndex修复和2025年7月22日参考日期...")
    
    # 测试1: DatetimeIndex访问修复
    test1_result = test_datetime_index_access()
    
    # 测试2: 2025年7月22日参考日期逻辑
    test2_result = test_reference_date_logic()
    
    # 测试3: 修复后的方法
    test3_result = simulate_fixed_find_closest_trading_day()
    
    if test1_result and test2_result and test3_result:
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！修复应该有效")
        print("修复要点:")
        print("1. 使用 dates[idx] 而不是 dates.iloc[idx]")
        print("2. 使用 2025年7月22日 作为参考日期")
        print("3. 分析过去5年同期历史数据进行统计预测")
        print("4. 展示一致性表现板块而非排行榜")
        print("=" * 60)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
