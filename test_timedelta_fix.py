#!/usr/bin/env python3
"""
测试numpy.timedelta64对象的正确处理
"""

import pandas as pd
import numpy as np

def test_timedelta64_conversion():
    """测试numpy.timedelta64对象转换为天数的不同方法"""
    print("=" * 60)
    print("测试numpy.timedelta64对象转换为天数")
    print("=" * 60)

    # 创建测试数据
    dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')
    target_date = pd.Timestamp('2024-01-05')

    print(f"可用日期: {dates}")
    print(f"目标日期: {target_date}")

    # 计算差异
    differences = abs(dates - target_date)
    min_diff_idx = differences.argmin()

    print(f"差异类型: {type(differences)}")
    print(f"最小差异索引: {min_diff_idx}")

    # 获取numpy.timedelta64对象
    differences_array = differences.values
    min_diff_timedelta = differences_array[min_diff_idx]

    print(f"差异数组类型: {type(differences_array)}")
    print(f"最小差异对象: {min_diff_timedelta}")
    print(f"最小差异对象类型: {type(min_diff_timedelta)}")

    # 测试不同的转换方法
    print("\n测试转换方法:")

    # 方法1: 使用pd.Timedelta除法
    try:
        days_1 = int(min_diff_timedelta / pd.Timedelta(days=1))
        print(f"✅ 方法1 - pd.Timedelta除法: {days_1} 天")
    except Exception as e:
        print(f"❌ 方法1失败: {e}")

    # 方法2: 使用numpy.timedelta64除法
    try:
        days_2 = int(min_diff_timedelta / np.timedelta64(1, 'D'))
        print(f"✅ 方法2 - numpy.timedelta64除法: {days_2} 天")
    except Exception as e:
        print(f"❌ 方法2失败: {e}")

    # 方法3: 转换为pandas Timedelta
    try:
        pd_timedelta = pd.Timedelta(min_diff_timedelta)
        days_3 = pd_timedelta.days
        print(f"✅ 方法3 - 转换为pandas Timedelta: {days_3} 天")
    except Exception as e:
        print(f"❌ 方法3失败: {e}")

    # 方法4: 使用astype转换
    try:
        days_4 = int(min_diff_timedelta.astype('timedelta64[D]').astype(int))
        print(f"✅ 方法4 - astype转换: {days_4} 天")
    except Exception as e:
        print(f"❌ 方法4失败: {e}")

    return True

def simulate_fixed_method():
    """模拟修复后的_find_closest_trading_day方法"""
    print("\n" + "=" * 60)
    print("模拟修复后的_find_closest_trading_day方法")
    print("=" * 60)

    def find_closest_trading_day_fixed(filtered_available_dates, target_date):
        """模拟修复后的方法"""
        if not filtered_available_dates:
            return None

        available_dates_ts = pd.to_datetime(filtered_available_dates)

        # 找到最接近的日期
        differences = abs(available_dates_ts - target_date)
        min_diff_idx = differences.argmin()

        # 获取最小差异的天数（正确处理numpy.timedelta64对象）
        differences_array = differences.values
        min_diff_timedelta = differences_array[min_diff_idx]
        # 将numpy.timedelta64转换为天数
        min_diff_days = int(min_diff_timedelta / pd.Timedelta(days=1))

        # 如果差异超过7天，认为没有合适的匹配
        if min_diff_days > 7:
            return None

        return available_dates_ts.iloc[min_diff_idx]

    # 测试数据
    available_dates = pd.date_range('2024-01-01', '2024-12-31', freq='B')  # 工作日
    test_cases = [
        pd.Timestamp('2024-07-22'),  # 工作日
        pd.Timestamp('2024-07-21'),  # 可能是周末
        pd.Timestamp('2024-12-25'),  # 可能是节假日
        pd.Timestamp('2019-01-01'),  # 超出范围
    ]

    print("测试用例:")
    success_count = 0
    for i, target_date in enumerate(test_cases, 1):
        try:
            result = find_closest_trading_day_fixed(available_dates, target_date)
            if result is not None:
                diff_days = abs((result - target_date).days)
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: {result.strftime('%Y-%m-%d')}, "
                      f"差异: {diff_days}天 ✅")
                success_count += 1
            else:
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: None (差异超过7天) ⚠️")
                success_count += 1
        except Exception as e:
            print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                  f"错误: {e} ❌")
            return False

    print(f"\n✅ 所有测试用例通过 ({success_count}/{len(test_cases)})")
    return True

def test_consistency_focus():
    """测试一致性表现板块的筛选逻辑"""
    print("\n" + "=" * 60)
    print("测试一致性表现板块筛选逻辑")
    print("=" * 60)

    # 创建模拟的板块表现数据
    sectors_data = [
        {'sector_name': '板块A', 'consistency_rate': 0.8, 'avg_return_pct': 5.2, 'positive_years': 4, 'total_years': 5},
        {'sector_name': '板块B', 'consistency_rate': 0.6, 'avg_return_pct': 8.1, 'positive_years': 3, 'total_years': 5},
        {'sector_name': '板块C', 'consistency_rate': 0.4, 'avg_return_pct': 12.3, 'positive_years': 2, 'total_years': 5},
        {'sector_name': '板块D', 'consistency_rate': 0.7, 'avg_return_pct': 3.8, 'positive_years': 3, 'total_years': 4},
    ]

    performance_df = pd.DataFrame(sectors_data)

    # 按一致性排序
    performance_df = performance_df.sort_values(['consistency_rate', 'avg_return_pct'], ascending=[False, False])

    print("所有板块（按一致性排序）:")
    for i, (_, sector) in enumerate(performance_df.iterrows(), 1):
        print(f"  {i}. {sector['sector_name']} - 一致性: {sector['consistency_rate']*100:.1f}%, "
              f"平均收益: {sector['avg_return_pct']:.2f}%")

    # 筛选一致性表现板块
    consistent_sectors = performance_df[performance_df['consistency_rate'] >= 0.5]

    print(f"\n一致性表现板块（一致性>=50%）:")
    if not consistent_sectors.empty:
        for i, (_, sector) in enumerate(consistent_sectors.iterrows(), 1):
            print(f"  {i}. {sector['sector_name']} - 一致性: {sector['consistency_rate']*100:.1f}% "
                  f"({sector['positive_years']}/{sector['total_years']}年), "
                  f"平均收益: {sector['avg_return_pct']:.2f}%")
    else:
        print("  未找到一致性>=50%的板块")

    return True

if __name__ == "__main__":
    print("开始测试numpy.timedelta64修复和一致性表现板块...")

    # 测试1: timedelta64转换
    test1_result = test_timedelta64_conversion()

    # 测试2: 修复后的方法
    test2_result = simulate_fixed_method()

    # 测试3: 一致性表现板块筛选
    test3_result = test_consistency_focus()

    if test1_result and test2_result and test3_result:
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！修复应该有效")
        print("修复要点:")
        print("1. 使用 pd.Timedelta(days=1) 进行除法运算")
        print("2. 将结果转换为整数天数")
        print("3. 重点关注一致性表现板块而非排行榜")
        print("=" * 60)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
