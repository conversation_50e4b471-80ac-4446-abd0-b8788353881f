#!/usr/bin/env python3
"""
最终测试：验证TimedeltaIndex访问修复
"""

import pandas as pd
import numpy as np

def test_timedelta_index_access():
    """测试TimedeltaIndex的正确访问方法"""
    print("=" * 60)
    print("测试TimedeltaIndex访问修复")
    print("=" * 60)
    
    # 创建测试数据
    available_dates = pd.date_range('2024-01-01', '2024-12-31', freq='B')  # 工作日
    target_date = pd.Timestamp('2024-07-22')
    
    print(f"可用日期数量: {len(available_dates)}")
    print(f"目标日期: {target_date}")
    
    # 模拟修复后的逻辑
    available_dates_ts = pd.to_datetime(available_dates)
    
    # 找到最接近的日期
    differences = abs(available_dates_ts - target_date)
    min_diff_idx = differences.argmin()
    
    print(f"差异类型: {type(differences)}")
    print(f"最小差异索引: {min_diff_idx}")
    
    # 测试修复后的方法
    try:
        # 获取最小差异的天数（转换为数组以避免TimedeltaIndex问题）
        differences_array = differences.values
        min_diff_timedelta = differences_array[min_diff_idx]
        min_diff_days = min_diff_timedelta.days
        
        print(f"✅ 修复后的方法成功:")
        print(f"   差异数组类型: {type(differences_array)}")
        print(f"   最小差异: {min_diff_timedelta}")
        print(f"   最小差异天数: {min_diff_days}")
        
        # 检查是否在7天内
        if min_diff_days > 7:
            closest_date = None
            print("❌ 差异超过7天，返回None")
        else:
            closest_date = available_dates_ts.iloc[min_diff_idx]
            print(f"✅ 找到最接近的交易日: {closest_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复后的方法仍然失败: {e}")
        return False

def simulate_complete_method():
    """模拟完整的_find_closest_trading_day方法"""
    print("\n" + "=" * 60)
    print("模拟完整的_find_closest_trading_day方法")
    print("=" * 60)
    
    def find_closest_trading_day(filtered_available_dates, target_date):
        """模拟修复后的方法"""
        if not filtered_available_dates:
            return None
            
        available_dates_ts = pd.to_datetime(filtered_available_dates)
        
        # 找到最接近的日期
        differences = abs(available_dates_ts - target_date)
        min_diff_idx = differences.argmin()
        
        # 获取最小差异的天数（转换为数组以避免TimedeltaIndex问题）
        differences_array = differences.values
        min_diff_timedelta = differences_array[min_diff_idx]
        min_diff_days = min_diff_timedelta.days
        
        # 如果差异超过7天，认为没有合适的匹配
        if min_diff_days > 7:
            return None
            
        return available_dates_ts.iloc[min_diff_idx]
    
    # 测试数据
    available_dates = pd.date_range('2024-01-01', '2024-12-31', freq='B')
    test_cases = [
        pd.Timestamp('2024-07-22'),  # 工作日
        pd.Timestamp('2024-07-21'),  # 可能是周末
        pd.Timestamp('2024-12-25'),  # 可能是节假日
        pd.Timestamp('2019-01-01'),  # 超出范围
    ]
    
    print("测试用例:")
    for i, target_date in enumerate(test_cases, 1):
        try:
            result = find_closest_trading_day(available_dates, target_date)
            if result is not None:
                diff_days = abs((result - target_date).days)
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: {result.strftime('%Y-%m-%d')}, "
                      f"差异: {diff_days}天 ✅")
            else:
                print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                      f"结果: None (差异超过7天) ⚠️")
        except Exception as e:
            print(f"  {i}. 目标: {target_date.strftime('%Y-%m-%d')}, "
                  f"错误: {e} ❌")
            return False
    
    return True

if __name__ == "__main__":
    print("开始最终测试...")
    
    # 测试1: TimedeltaIndex访问修复
    test1_result = test_timedelta_index_access()
    
    # 测试2: 完整方法模拟
    test2_result = simulate_complete_method()
    
    if test1_result and test2_result:
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！修复应该有效")
        print("修复要点:")
        print("1. 使用 differences.values 转换为数组")
        print("2. 通过数组索引访问 TimeDelta 对象")
        print("3. 避免直接对 TimedeltaIndex 使用 .iloc")
        print("=" * 60)
    else:
        print("\n❌ 测试失败，需要进一步检查")
