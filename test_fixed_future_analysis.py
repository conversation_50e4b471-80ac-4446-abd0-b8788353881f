#!/usr/bin/env python3
"""
测试修复后的未来时间窗口分析功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_analysis():
    """测试修复后的分析功能"""
    print("🧪 测试修复后的未来时间窗口分析功能")
    print("=" * 60)
    
    try:
        # 清除模块缓存
        modules_to_clear = ['main', 'config', 'src.time_window_analyzer']
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        # 导入模块
        import config
        from main import parse_arguments, validate_arguments
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        print("✅ 模块导入成功")
        
        # 模拟参数解析
        original_argv = sys.argv.copy()
        sys.argv = ['main.py']
        
        try:
            args = parse_arguments()
            args = validate_arguments(args)
            
            print(f"\n📋 参数验证结果:")
            print(f"   历史时间窗口: {args.windows}")
            print(f"   未来时间窗口: {args.future_windows}")
            print(f"   未来分析启用: {args.enable_future_analysis}")
            
            # 加载测试数据
            print(f"\n📊 数据加载:")
            loader = DataLoader()
            loader.file_list = loader.file_list[:10]  # 加载10个文件进行测试
            data = loader.load_all_data()
            print(f"   数据形状: {data.shape}")
            
            # 初始化分析器
            print(f"\n🔧 分析器初始化:")
            analyzer = TimeWindowAnalyzer(data)
            print(f"   全部数据日期范围: {analyzer.date_range}")
            print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
            
            # 测试日期选择逻辑
            print(f"\n📅 日期选择逻辑测试:")
            latest_date = analyzer.available_dates[-1]
            print(f"   最新数据日期: {latest_date}")
            print(f"   最新数据年份: {latest_date.year}")
            
            # 应用修复后的日期选择逻辑
            if latest_date.year >= 2025 and analyzer.filtered_available_dates:
                end_date = analyzer.filtered_available_dates[-1]
                print(f"   ✅ 选择过滤数据的最后日期: {end_date}")
                print(f"   ✅ 这样可以确保未来分析有足够的数据")
            else:
                end_date = latest_date
                print(f"   使用最新日期: {end_date}")
            
            # 测试历史分析
            print(f"\n📈 历史时间窗口分析测试:")
            window_results = {}
            for window_days in args.windows:
                try:
                    performance = analyzer.calculate_window_performance(end_date, window_days)
                    if not performance.empty:
                        window_results[window_days] = performance
                        top_sector = performance.iloc[0]
                        print(f"   历史{window_days:2d}日窗口: ✅ {len(performance)} 个板块, 最佳: {top_sector['sector_name']} ({top_sector['cumulative_return_pct']:.2f}%)")
                    else:
                        print(f"   历史{window_days:2d}日窗口: ⚠️ 无数据")
                except Exception as e:
                    print(f"   历史{window_days:2d}日窗口: ❌ 错误 - {str(e)}")
            
            # 测试未来分析
            print(f"\n🔮 未来时间窗口分析测试:")
            future_window_results = {}
            for window_days in args.future_windows:
                try:
                    performance = analyzer.calculate_future_window_performance(end_date, window_days)
                    if not performance.empty:
                        future_window_results[window_days] = performance
                        top_sector = performance.iloc[0]
                        print(f"   未来{window_days:2d}日窗口: ✅ {len(performance)} 个板块, 最佳: {top_sector['sector_name']} ({top_sector['cumulative_return_pct']:.2f}%)")
                        
                        # 显示时间范围
                        start_date = top_sector['window_start_date']
                        end_date_window = top_sector['window_end_date']
                        print(f"       时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date_window.strftime('%Y-%m-%d')}")
                    else:
                        print(f"   未来{window_days:2d}日窗口: ⚠️ 无数据")
                except Exception as e:
                    print(f"   未来{window_days:2d}日窗口: ❌ 错误 - {str(e)}")
            
            # 统计结果
            total_windows = len(window_results) + len(future_window_results)
            print(f"\n📊 分析结果统计:")
            print(f"   历史窗口: {len(window_results)} 个")
            print(f"   未来窗口: {len(future_window_results)} 个")
            print(f"   总计: {total_windows} 个时间窗口")
            
            # 验证结果
            expected_total = len(args.windows) + len(args.future_windows)
            if total_windows == expected_total:
                print(f"   ✅ 结果正确！获得了预期的 {expected_total} 个时间窗口")
                return True
            else:
                print(f"   ❌ 结果不符合预期！期望 {expected_total} 个，实际 {total_windows} 个")
                return False
            
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_program():
    """测试完整的main程序"""
    print(f"\n🚀 测试完整的main程序")
    print("-" * 40)
    
    try:
        import subprocess
        import sys
        
        # 运行main程序（快速模式）
        print("运行: python main.py --quick-mode")
        result = subprocess.run([
            sys.executable, 'main.py', '--quick-mode'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            output = result.stdout
            
            # 检查输出中是否包含未来分析
            if "未来时间窗口分析" in output:
                print("✅ 输出包含未来时间窗口分析")
            else:
                print("❌ 输出不包含未来时间窗口分析")
                return False
            
            # 检查是否有6个时间窗口
            if "共分析 6 个时间窗口" in output:
                print("✅ 输出显示6个时间窗口")
                return True
            elif "共分析 3 个时间窗口" in output:
                print("❌ 输出仍然只显示3个时间窗口")
                return False
            else:
                print("⚠️ 无法确定时间窗口数量")
                return False
        else:
            print(f"❌ 程序执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试修复后的未来时间窗口分析功能")
    print("=" * 60)
    
    # 测试修复后的分析功能
    success1 = test_fixed_analysis()
    
    if success1:
        print("\n🎉 单元测试通过！")
        
        # 测试完整程序
        success2 = test_main_program()
        
        if success2:
            print("\n🎉 所有测试通过！修复成功！")
            print("✨ 用户现在可以运行 'python main.py' 获得完整的6个时间窗口分析结果")
        else:
            print("\n💥 完整程序测试失败，需要进一步调试")
    else:
        print("\n💥 单元测试失败，修复不完整")
    
    return success1

if __name__ == "__main__":
    main()
