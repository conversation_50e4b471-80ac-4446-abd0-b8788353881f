#!/usr/bin/env python3
"""
直接测试main.py中的函数
"""

import sys
import os
import importlib

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_date_selection():
    """直接测试日期选择逻辑"""
    print("🧪 直接测试日期选择逻辑")
    print("=" * 60)
    
    try:
        # 清除模块缓存
        for module in list(sys.modules.keys()):
            if module.startswith('main') or module.startswith('src.') or module == 'config':
                del sys.modules[module]
        
        # 导入模块
        import config
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        # 加载少量数据
        loader = DataLoader()
        loader.file_list = loader.file_list[:5]  # 只加载5个文件
        data = loader.load_all_data()
        
        # 初始化分析器
        analyzer = TimeWindowAnalyzer(data)
        
        # 测试日期选择逻辑
        latest_date = analyzer.available_dates[-1]
        print(f"最新数据日期: {latest_date}")
        
        # 应用修复后的日期选择逻辑
        if latest_date.year >= 2025 and analyzer.filtered_available_dates:
            # 选择过滤数据中的最后一个日期（2024年或更早）
            end_date = analyzer.filtered_available_dates[-1]
            print(f"✅ 选择过滤数据的最后日期: {end_date}")
        else:
            end_date = latest_date
            print(f"使用最新日期: {end_date}")
        
        # 测试未来分析
        print(f"\n🔮 使用选定日期测试未来分析:")
        for window_days in config.FUTURE_TIME_WINDOWS:
            try:
                performance = analyzer.calculate_future_window_performance(end_date, window_days)
                if not performance.empty:
                    print(f"   未来{window_days:2d}日窗口: ✅ {len(performance)} 个板块")
                else:
                    print(f"   未来{window_days:2d}日窗口: ⚠️ 无数据")
            except Exception as e:
                print(f"   未来{window_days:2d}日窗口: ❌ 错误 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_modified_main():
    """测试修改后的main.py"""
    print(f"\n🚀 测试修改后的main.py")
    print("-" * 40)
    
    try:
        # 清除模块缓存
        for module in list(sys.modules.keys()):
            if module.startswith('main') or module.startswith('src.') or module == 'config':
                del sys.modules[module]
        
        # 重新导入main模块
        import main
        importlib.reload(main)
        
        # 创建一个测试函数，直接调用main.py中的代码
        def test_main_code():
            # 模拟参数
            class Args:
                end_date = None
                windows = [7, 14, 30]
                future_windows = [30, 60, 90]
                enable_future_analysis = True
                verbose = True
                quick_mode = True
                no_charts = True
                export_csv = False
                enable_historical = True
                top_n = 5
                output_dir = "output"
                max_files = None
            
            args = Args()
            
            # 加载少量数据
            from src.data_loader import DataLoader
            loader = DataLoader()
            loader.file_list = loader.file_list[:5]  # 只加载5个文件
            data = loader.load_all_data()
            
            # 初始化分析器
            from src.time_window_analyzer import TimeWindowAnalyzer
            analyzer = TimeWindowAnalyzer(data)
            
            # 确定分析参考日期 - 这是我们修改的部分
            if args.end_date:
                end_date = args.end_date
            else:
                # 为了确保未来分析有足够的数据，选择一个合适的历史日期
                # 如果最新日期是2025年，选择2024年的最后一个交易日作为参考点
                latest_date = analyzer.available_dates[-1]
                if latest_date.year >= 2025 and analyzer.filtered_available_dates:
                    # 选择过滤数据中的最后一个日期（2024年或更早）
                    end_date = analyzer.filtered_available_dates[-1]
                    print(f"📅 为确保未来分析数据完整性，使用 {end_date.strftime('%Y-%m-%d')} 作为分析参考日期")
                else:
                    end_date = latest_date
            
            print(f"分析参考日期: {end_date}")
            
            # 执行历史时间窗口分析
            window_results = {}
            for window_days in args.windows:
                performance = analyzer.calculate_window_performance(end_date, window_days)
                if not performance.empty:
                    window_results[window_days] = performance
                    print(f"  历史{window_days:2d}日窗口: {len(performance)} 个板块")
            
            # 执行未来时间窗口分析
            future_window_results = {}
            print(f"\n开始未来时间窗口分析（基于≤2024年数据）...")
            for window_days in args.future_windows:
                performance = analyzer.calculate_future_window_performance(end_date, window_days)
                if not performance.empty:
                    future_window_results[window_days] = performance
                    print(f"  未来{window_days:2d}日窗口: {len(performance)} 个板块")
            
            # 统计结果
            total_windows = len(window_results) + len(future_window_results)
            print(f"✅ 时间窗口分析完成，共分析 {total_windows} 个时间窗口")
            print(f"   - 历史窗口: {len(window_results)} 个")
            print(f"   - 未来窗口: {len(future_window_results)} 个")
            
            return total_windows
        
        # 执行测试
        total_windows = test_main_code()
        
        # 验证结果
        if total_windows == 6:
            print(f"\n✅ 测试通过！获得了预期的6个时间窗口")
            return True
        else:
            print(f"\n❌ 测试失败！期望6个时间窗口，实际获得{total_windows}个")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始直接测试main.py中的函数")
    print("=" * 60)
    
    # 测试日期选择逻辑
    success1 = test_date_selection()
    
    # 测试修改后的main.py
    success2 = test_modified_main()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！修复成功！")
        print("✨ 修复方案：")
        print("   1. 当最新日期是2025年时，自动选择2024年的最后一个交易日作为分析参考点")
        print("   2. 这样可以确保未来分析有足够的数据")
        print("   3. 用户无需手动指定日期，程序会自动选择合适的日期")
    else:
        print("\n💥 测试失败，需要进一步调试")
    
    return success1 and success2

if __name__ == "__main__":
    main()
