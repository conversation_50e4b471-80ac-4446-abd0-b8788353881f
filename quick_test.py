#!/usr/bin/env python3
"""
快速测试numpy.timedelta64修复
"""

import pandas as pd
import numpy as np

def test_timedelta_conversion():
    """测试不同的timedelta转换方法"""
    print("测试numpy.timedelta64转换方法...")
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')
    target_date = pd.Timestamp('2024-01-05')
    
    # 计算差异
    differences = abs(dates - target_date)
    min_diff_idx = differences.argmin()
    
    print(f"差异类型: {type(differences)}")
    print(f"最小差异索引: {min_diff_idx}")
    
    # 获取numpy.timedelta64对象
    differences_array = differences.values
    min_diff_timedelta = differences_array[min_diff_idx]
    
    print(f"最小差异对象: {min_diff_timedelta}")
    print(f"最小差异对象类型: {type(min_diff_timedelta)}")
    
    # 测试修复方法
    print("\n测试修复方法:")
    
    # 方法1: 使用pd.Timedelta除法
    try:
        days_1 = int(min_diff_timedelta / pd.Timedelta(days=1))
        print(f"✅ 方法1 - pd.Timedelta除法: {days_1} 天")
        return True
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: 转换为pandas Timedelta
    try:
        pd_timedelta = pd.Timedelta(min_diff_timedelta)
        days_2 = pd_timedelta.days
        print(f"✅ 方法2 - 转换为pandas Timedelta: {days_2} 天")
        return True
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: 使用numpy除法
    try:
        days_3 = int(min_diff_timedelta / np.timedelta64(1, 'D'))
        print(f"✅ 方法3 - numpy除法: {days_3} 天")
        return True
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    return False

if __name__ == "__main__":
    success = test_timedelta_conversion()
    if success:
        print("\n✅ 修复方法有效！")
    else:
        print("\n❌ 所有方法都失败了")
