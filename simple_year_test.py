#!/usr/bin/env python3
"""
简单测试年份收益率数据结构
"""

import pandas as pd
import numpy as np

# 模拟年份收益率映射
year_returns_dict = {
    2020: 4.65,
    2021: 3.84,
    2022: 2.32,
    2023: 1.15,
    2024: 7.75
}

historical_years = [2020, 2021, 2022, 2023, 2024]

# 测试格式化逻辑
if isinstance(historical_years, list) and year_returns_dict:
    # 格式化为：年份(收益率%)
    years_with_returns = []
    for year in sorted(historical_years):
        if year in year_returns_dict:
            return_rate = year_returns_dict[year]
            years_with_returns.append(f"{year}({return_rate:.1f}%)")
        else:
            years_with_returns.append(str(year))
    years_str = ', '.join(years_with_returns)
elif isinstance(historical_years, list):
    # 如果没有收益率数据，只显示年份
    years_str = ', '.join(map(str, sorted(historical_years)))
else:
    years_str = str(historical_years)

print("测试年份收益率格式化:")
print(f"原始年份: {historical_years}")
print(f"年份收益率映射: {year_returns_dict}")
print(f"格式化结果: {years_str}")

# 期望结果
expected = "2020(4.7%), 2021(3.8%), 2022(2.3%), 2023(1.2%), 2024(7.8%)"
print(f"期望格式: {expected}")

print("\n✅ 格式化逻辑测试完成")
