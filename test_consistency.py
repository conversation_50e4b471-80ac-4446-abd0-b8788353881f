#!/usr/bin/env python3
"""
测试一致性率计算是否正确
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append('src')

from data_loader import DataLoader
from time_window_analyzer import TimeWindowAnalyzer
import config

def test_consistency_calculation():
    """测试一致性率计算"""
    print("=" * 60)
    print("测试一致性率计算")
    print("=" * 60)

    # 检查配置
    analysis_years_count = config.DATA_YEAR_LIMIT.get('analysis_years_count', 5)
    print(f"配置的分析年份数: {analysis_years_count}")

    # 加载数据
    print("正在加载数据...")
    loader = DataLoader(config.DATA_DIR)
    data = loader.load_all_data()

    if data.empty:
        print("❌ 数据加载失败")
        return

    print(f"✅ 数据加载成功，共 {len(data)} 条记录")

    # 创建分析器
    analyzer = TimeWindowAnalyzer(data)

    # 获取最新日期
    end_date = analyzer.available_dates[-1]
    print(f"参考日期: {end_date}")

    # 分析未来30天窗口
    print(f"\n分析未来30天窗口...")
    performance = analyzer.calculate_future_window_performance(end_date, 30)

    if performance.empty:
        print("❌ 未来窗口分析失败")
        return

    print(f"✅ 分析完成，共 {len(performance)} 个板块")

    # 检查房地产服务板块
    real_estate_service = performance[performance['sector_name'] == '房地产服务']

    if not real_estate_service.empty:
        sector = real_estate_service.iloc[0]
        print(f"\n🏠 房地产服务板块分析结果:")
        print(f"   板块代码: {sector['sector_code']}")
        print(f"   正收益年份: {sector['positive_years']}")
        print(f"   实际数据年份: {sector.get('actual_years', '未知')}")
        print(f"   总分析年份: {sector['total_years']}")
        print(f"   一致性率: {sector['consistency_rate']:.3f} ({sector['consistency_percentage']:.1f}%)")
        print(f"   历史年份: {sector.get('historical_years', [])}")

        # 验证计算是否正确
        expected_rate = sector['positive_years'] / analysis_years_count
        print(f"\n✅ 验证计算:")
        print(f"   期望一致性率: {sector['positive_years']}/{analysis_years_count} = {expected_rate:.3f} ({expected_rate*100:.1f}%)")
        print(f"   实际一致性率: {sector['consistency_rate']:.3f} ({sector['consistency_percentage']:.1f}%)")

        if abs(sector['consistency_rate'] - expected_rate) < 0.001:
            print(f"   ✅ 计算正确！")
        else:
            print(f"   ❌ 计算错误！")
    else:
        print("❌ 未找到房地产服务板块")

    # 显示所有一致性率>=50%的板块
    consistent_sectors = performance[performance['consistency_rate'] >= 0.5]
    print(f"\n📊 一致性率≥50%的板块 (共{len(consistent_sectors)}个):")
    print("-" * 90)
    print(f"{'板块名称':<12} {'正收益年份':<8} {'实际年份':<8} {'总年份':<6} {'一致性率':<8} {'修正前':<8}")
    print("-" * 90)

    for _, sector in consistent_sectors.head(10).iterrows():
        actual_years = sector.get('actual_years', sector['positive_years'])
        old_rate = sector['positive_years'] / actual_years * 100 if actual_years > 0 else 0
        print(f"{sector['sector_name']:<12} {sector['positive_years']:<8} {actual_years:<8} {sector['total_years']:<6} "
              f"{sector['consistency_percentage']:>6.1f}% {old_rate:>6.1f}%")

if __name__ == "__main__":
    test_consistency_calculation()
