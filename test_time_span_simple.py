#!/usr/bin/env python3
"""
简单测试时间跨度计算功能
"""

import os
import sys

# 防止导入时触发main.py
if 'main' in sys.modules:
    del sys.modules['main']

import pandas as pd

def calculate_historical_time_span_for_year(end_date: str, window_days: int, year: int) -> str:
    """
    计算特定年份历史时间窗口的准确时间跨度
    
    Args:
        end_date: 结束日期
        window_days: 窗口天数
        year: 年份
        
    Returns:
        时间跨度字符串，格式为"YYYY年MM月DD日到YYYY年MM月DD日"
    """
    try:
        if not end_date:
            return ""
            
        end_dt = pd.to_datetime(end_date)
        
        # 计算近似的开始日期（考虑到交易日的影响）
        # 假设每周有5个交易日，所以需要的自然日数约为 window_days * 7 / 5
        approx_calendar_days = int(window_days * 1.4)  # 1.4 ≈ 7/5
        approx_start_date = end_dt - pd.Timedelta(days=approx_calendar_days)
        
        return f"{approx_start_date.strftime('%Y年%m月%d日')}到{end_dt.strftime('%Y年%m月%d日')}"
    except Exception as e:
        print(f"计算历史时间跨度失败: {str(e)}")
        return ""

def test_time_span_calculation():
    """测试时间跨度计算方法"""
    print("=" * 60)
    print("测试历史时间跨度计算功能")
    print("=" * 60)
    
    # 测试历史时间跨度计算
    print("📊 历史时间窗口时间跨度:")
    print("-" * 40)
    
    end_date = '2025-07-23'
    for window_days in [7, 14, 30]:
        for year in [2024, 2025]:
            hist_span = calculate_historical_time_span_for_year(end_date, window_days, year)
            print(f"{year}年{window_days:2d}日时间窗口: {hist_span}")
    
    print()
    print("✅ 时间跨度计算测试完成")

if __name__ == "__main__":
    test_time_span_calculation()
