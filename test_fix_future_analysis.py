#!/usr/bin/env python3
"""
测试修复后的未来时间窗口分析功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_find_closest_trading_day():
    """测试_find_closest_trading_day方法的修复"""
    print("=" * 60)
    print("测试 _find_closest_trading_day 方法修复")
    print("=" * 60)
    
    try:
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        # 创建模拟数据
        print("1. 创建模拟数据...")
        dates = pd.date_range('2020-01-01', '2024-12-31', freq='D')
        sectors = ['BK0001', 'BK0002']
        
        data_list = []
        for date in dates:
            for sector in sectors:
                data_list.append({
                    'date': date,
                    'sector_code': sector,
                    'sector_name': f'板块{sector[-1]}',
                    'change_pct': np.random.normal(0, 2)
                })
        
        df = pd.DataFrame(data_list)
        df = df.set_index(['date', 'sector_code'])
        print(f"   模拟数据创建完成: {len(df)} 条记录")
        
        # 初始化分析器
        print("2. 初始化时间窗口分析器...")
        analyzer = TimeWindowAnalyzer(df)
        print(f"   可用日期数量: {len(analyzer.filtered_available_dates)}")
        
        # 测试_find_closest_trading_day方法
        print("3. 测试 _find_closest_trading_day 方法...")
        
        # 测试用例1：精确匹配
        target_date1 = pd.Timestamp('2024-07-22')
        closest_date1 = analyzer._find_closest_trading_day(target_date1)
        print(f"   测试1 - 目标日期: {target_date1}, 最接近日期: {closest_date1}")
        
        # 测试用例2：周末日期（应该找到最近的工作日）
        target_date2 = pd.Timestamp('2024-07-21')  # 假设是周末
        closest_date2 = analyzer._find_closest_trading_day(target_date2)
        print(f"   测试2 - 目标日期: {target_date2}, 最接近日期: {closest_date2}")
        
        # 测试用例3：超出范围的日期（应该返回None）
        target_date3 = pd.Timestamp('2019-01-01')  # 超出数据范围
        closest_date3 = analyzer._find_closest_trading_day(target_date3)
        print(f"   测试3 - 目标日期: {target_date3}, 最接近日期: {closest_date3}")
        
        print("   ✅ _find_closest_trading_day 方法测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_future_window_analysis():
    """测试未来时间窗口分析功能"""
    print("\n" + "=" * 60)
    print("测试未来时间窗口分析功能")
    print("=" * 60)
    
    try:
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        # 创建更完整的模拟数据
        print("1. 创建完整的模拟数据...")
        dates = pd.date_range('2019-01-01', '2024-12-31', freq='D')
        sectors = ['BK0001', 'BK0002', 'BK0003']
        
        data_list = []
        for date in dates:
            for sector in sectors:
                # 模拟不同板块的表现特征
                if sector == 'BK0001':
                    change_pct = np.random.normal(0.1, 1.5)  # 稍微偏正的收益
                elif sector == 'BK0002':
                    change_pct = np.random.normal(-0.05, 2.0)  # 稍微偏负的收益
                else:
                    change_pct = np.random.normal(0, 1.8)  # 中性收益
                    
                data_list.append({
                    'date': date,
                    'sector_code': sector,
                    'sector_name': f'板块{sector[-1]}',
                    'change_pct': change_pct,
                    'open': 100.0,
                    'close': 100.0 + change_pct,
                    'high': 105.0,
                    'low': 95.0,
                    'volume': 1000000,
                    'amount': 100000000,
                    'amplitude': abs(change_pct),
                    'change_amount': change_pct,
                    'turnover_rate': 5.0
                })
        
        df = pd.DataFrame(data_list)
        df = df.set_index(['date', 'sector_code'])
        print(f"   模拟数据创建完成: {len(df)} 条记录")
        
        # 初始化分析器
        print("2. 初始化时间窗口分析器...")
        analyzer = TimeWindowAnalyzer(df)
        print(f"   数据日期范围: {analyzer.date_range}")
        print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
        
        # 测试未来时间窗口分析
        print("3. 测试未来时间窗口分析...")
        start_date = pd.Timestamp('2024-07-22')
        window_days = 30
        
        print(f"   参考日期: {start_date}")
        print(f"   时间窗口: {window_days} 天")
        
        result = analyzer.calculate_future_window_performance(start_date, window_days)
        
        if result.empty:
            print("   ❌ 分析结果为空")
            return False
        
        print(f"   ✅ 分析完成，共 {len(result)} 个板块")
        print(f"   结果列: {list(result.columns)}")
        
        # 显示前3个板块的结果
        print("\n4. 分析结果（前3个板块）:")
        print("-" * 80)
        for i, (_, row) in enumerate(result.head(3).iterrows()):
            print(f"   {i+1}. {row['sector_name']} ({row['sector_code']})")
            if 'avg_return_pct' in row:
                print(f"      平均收益率: {row['avg_return_pct']:.2f}%")
            if 'consistency_percentage' in row:
                print(f"      一致性: {row['consistency_percentage']:.1f}%")
            if 'historical_years' in row:
                print(f"      分析年份: {row['historical_years']}")
            print()
        
        print("   ✅ 未来时间窗口分析测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修复后的未来时间窗口分析功能...")
    
    # 测试1: _find_closest_trading_day方法修复
    test1_result = test_find_closest_trading_day()
    
    # 测试2: 未来时间窗口分析功能
    test2_result = test_future_window_analysis()
    
    if test1_result and test2_result:
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！修复成功")
        print("=" * 60)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
