#!/usr/bin/env python3
"""
测试未来时间窗口分析的修正版本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.time_window_analyzer import TimeWindowAnalyzer
from src.data_loader import DataLoader
import pandas as pd

def test_future_analysis():
    print("=" * 60)
    print("测试未来时间窗口分析（基于历史同期数据）")
    print("=" * 60)
    
    # 加载少量数据进行测试
    print("1. 加载测试数据...")
    loader = DataLoader()
    loader.file_list = loader.file_list[:20]  # 只加载20个文件进行快速测试
    data = loader.load_all_data()
    print(f"   数据加载完成: {len(data)} 条记录")
    
    # 初始化分析器
    print("2. 初始化时间窗口分析器...")
    analyzer = TimeWindowAnalyzer(data)
    print(f"   可用日期范围: {analyzer.date_range}")
    print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
    
    # 测试新的未来时间窗口分析
    print("3. 测试未来时间窗口分析...")
    start_date = pd.Timestamp('2024-07-22')  # 使用一个历史日期作为参考
    print(f"   参考日期: {start_date}")
    
    # 测试30日窗口
    print("   分析30日时间窗口...")
    result = analyzer.calculate_future_window_performance(start_date, 30)
    
    if result.empty:
        print("   ❌ 分析结果为空")
        return False
    
    print(f"   ✅ 分析完成，共 {len(result)} 个板块")
    print(f"   结果列: {list(result.columns)}")
    
    # 显示前5个板块的结果
    print("\n4. 分析结果（前5个板块）:")
    print("-" * 80)
    for i, (_, row) in enumerate(result.head(5).iterrows()):
        print(f"   {i+1}. {row['sector_name']} ({row['sector_code']})")
        if 'avg_return_pct' in row:
            print(f"      平均收益率: {row['avg_return_pct']:.2f}%")
        if 'consistency_percentage' in row:
            print(f"      一致性: {row['consistency_percentage']:.1f}%")
        if 'historical_years' in row:
            print(f"      分析年份: {row['historical_years']}")
        print()
    
    return True

if __name__ == "__main__":
    try:
        success = test_future_analysis()
        if success:
            print("✅ 测试成功完成！")
        else:
            print("❌ 测试失败！")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
