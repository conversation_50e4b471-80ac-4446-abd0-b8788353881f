#!/usr/bin/env python3
"""
独立测试TimedeltaIndex访问修复
"""

import pandas as pd
import numpy as np

def test_timedelta_access_methods():
    """测试不同的TimedeltaIndex访问方法"""
    print("=" * 60)
    print("测试TimedeltaIndex访问方法")
    print("=" * 60)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')
    target_date = pd.Timestamp('2024-01-05')
    
    print(f"可用日期: {dates}")
    print(f"目标日期: {target_date}")
    
    # 计算差异
    differences = abs(dates - target_date)
    print(f"差异: {differences}")
    print(f"差异类型: {type(differences)}")
    
    # 找到最小差异的索引
    min_diff_idx = differences.argmin()
    print(f"最小差异索引: {min_diff_idx}")
    
    # 测试不同的访问方法
    print("\n测试访问方法:")
    
    # 方法1: 直接索引访问（可能失败）
    try:
        min_diff_1 = differences[min_diff_idx]
        print(f"✅ 方法1 - differences[idx]: {min_diff_1}, 天数: {min_diff_1.days}")
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: .values数组访问（推荐）
    try:
        min_diff_2 = differences.values[min_diff_idx]
        print(f"✅ 方法2 - differences.values[idx]: {min_diff_2}, 天数: {min_diff_2.days}")
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: .iloc访问（可能失败）
    try:
        min_diff_3 = differences.iloc[min_diff_idx]
        print(f"✅ 方法3 - differences.iloc[idx]: {min_diff_3}, 天数: {min_diff_3.days}")
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    # 方法4: 转换为Series后访问
    try:
        diff_series = pd.Series(differences)
        min_diff_4 = diff_series.iloc[min_diff_idx]
        print(f"✅ 方法4 - Series.iloc[idx]: {min_diff_4}, 天数: {min_diff_4.days}")
    except Exception as e:
        print(f"❌ 方法4失败: {e}")
    
    print("\n结论: 使用 differences.values[idx] 是最安全的方法")

def simulate_find_closest_trading_day():
    """模拟修复后的_find_closest_trading_day方法"""
    print("\n" + "=" * 60)
    print("模拟修复后的_find_closest_trading_day方法")
    print("=" * 60)
    
    # 模拟可用交易日
    available_dates = pd.date_range('2024-01-01', '2024-12-31', freq='B')  # 工作日
    target_date = pd.Timestamp('2024-07-22')
    
    print(f"可用交易日数量: {len(available_dates)}")
    print(f"目标日期: {target_date}")
    
    # 模拟修复后的逻辑
    available_dates_ts = pd.to_datetime(available_dates)
    
    # 找到最接近的日期
    differences = abs(available_dates_ts - target_date)
    min_diff_idx = differences.argmin()
    
    # 使用修复后的方法获取最小差异的天数
    min_diff_timedelta = differences.values[min_diff_idx]
    min_diff_days = min_diff_timedelta.days
    
    print(f"最小差异: {min_diff_timedelta}")
    print(f"最小差异天数: {min_diff_days}")
    
    # 检查是否在7天内
    if min_diff_days > 7:
        closest_date = None
        print("❌ 差异超过7天，返回None")
    else:
        closest_date = available_dates_ts.iloc[min_diff_idx]
        print(f"✅ 找到最接近的交易日: {closest_date}")
    
    return closest_date

if __name__ == "__main__":
    print("开始测试TimedeltaIndex访问修复...")
    
    # 测试1: 不同访问方法
    test_timedelta_access_methods()
    
    # 测试2: 模拟修复后的方法
    result = simulate_find_closest_trading_day()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！修复应该有效")
    print("=" * 60)
