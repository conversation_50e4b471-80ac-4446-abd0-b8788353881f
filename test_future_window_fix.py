#!/usr/bin/env python3
"""
测试未来时间窗口数据流修复
"""

import pandas as pd
import os
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from report_generator import ReportGenerator

def create_test_data():
    """创建测试数据"""
    
    # 模拟历史时间窗口数据
    window_results = {
        7: pd.DataFrame({
            'sector_name': ['板块A', '板块B', '板块C'],
            'sector_code': ['BK001', 'BK002', 'BK003'],
            'cumulative_return_pct': [5.2, 3.8, 2.1],
            'avg_daily_change_pct': [0.74, 0.54, 0.30],
            'volatility': [2.1, 1.8, 1.5]
        }),
        14: pd.DataFrame({
            'sector_name': ['板块B', '板块A', '板块D'],
            'sector_code': ['BK002', 'BK001', 'BK004'],
            'cumulative_return_pct': [8.1, 7.3, 4.2],
            'avg_daily_change_pct': [0.58, 0.52, 0.30],
            'volatility': [2.3, 2.0, 1.7]
        }),
        30: pd.DataFrame({
            'sector_name': ['板块C', '板块A', '板块B'],
            'sector_code': ['BK003', 'BK001', 'BK002'],
            'cumulative_return_pct': [12.5, 10.8, 9.2],
            'avg_daily_change_pct': [0.42, 0.36, 0.31],
            'volatility': [2.8, 2.5, 2.2]
        })
    }
    
    # 模拟未来时间窗口数据（包含一致性信息）
    future_window_results = {
        30: pd.DataFrame({
            'sector_name': ['证券', '房地产服务', '石油行业', '板块D'],
            'sector_code': ['BK001', 'BK002', 'BK003', 'BK004'],
            'avg_return_pct': [4.90, 3.12, 4.59, 1.5],
            'consistency_rate': [1.0, 1.0, 0.8, 0.4],
            'consistency_percentage': [100.0, 100.0, 80.0, 40.0],
            'positive_years': [5, 3, 4, 2],
            'total_years': [5, 3, 5, 5],
            'historical_years': [[2020, 2021, 2022, 2023, 2024], [2022, 2023, 2024], [2020, 2021, 2023, 2024], [2021, 2023]]
        }),
        60: pd.DataFrame({
            'sector_name': ['煤炭行业', '航空机场', '燃气'],
            'sector_code': ['BK002', 'BK001', 'BK005'],
            'avg_return_pct': [8.81, 0.63, 8.42],
            'consistency_rate': [0.8, 0.8, 0.75],
            'consistency_percentage': [80.0, 80.0, 75.0],
            'positive_years': [4, 4, 3],
            'total_years': [5, 5, 4],
            'historical_years': [[2020, 2021, 2023, 2024], [2021, 2022, 2023, 2024], [2022, 2023, 2024]]
        }),
        90: pd.DataFrame({
            'sector_name': ['仪器仪表', '燃气', '通用设备'],
            'sector_code': ['BK001', 'BK006', 'BK002'],
            'avg_return_pct': [19.90, 19.18, 17.66],
            'consistency_rate': [1.0, 1.0, 1.0],
            'consistency_percentage': [100.0, 100.0, 100.0],
            'positive_years': [5, 4, 5],
            'total_years': [5, 4, 5],
            'historical_years': [[2020, 2021, 2022, 2023, 2024], [2021, 2022, 2023, 2024], [2020, 2021, 2022, 2023, 2024]]
        })
    }
    
    # 模拟多年份历史数据（这是导致问题的关键）
    historical_analysis = {
        'multi_year_rankings': {
            7: {
                2020: pd.DataFrame({'sector_name': ['板块A'], 'cumulative_return_pct': [5.0]}),
                2021: pd.DataFrame({'sector_name': ['板块B'], 'cumulative_return_pct': [6.0]}),
                2022: pd.DataFrame({'sector_name': ['板块C'], 'cumulative_return_pct': [4.0]}),
            },
            14: {
                2020: pd.DataFrame({'sector_name': ['板块B'], 'cumulative_return_pct': [8.0]}),
                2021: pd.DataFrame({'sector_name': ['板块A'], 'cumulative_return_pct': [7.0]}),
                2022: pd.DataFrame({'sector_name': ['板块D'], 'cumulative_return_pct': [9.0]}),
            }
        },
        'consistency_analysis': {}
    }
    
    # 其他数据
    champions = pd.DataFrame({
        'rank': [1, 2, 3],
        'sector_name': ['板块A', '板块B', '板块C'],
        'sector_code': ['BK001', 'BK002', 'BK003'],
        'champion_count': [15, 12, 8],
        'champion_frequency_pct': [3.2, 2.6, 1.7],
        'avg_champion_change_pct': [8.5, 7.2, 6.8]
    })
    
    ranking_frequency = pd.DataFrame({
        'rank': [1, 2, 3],
        'sector_name': ['板块A', '板块B', '板块C'],
        'sector_code': ['BK001', 'BK002', 'BK003'],
        'top10_count': [45, 38, 32],
        'top10_frequency_pct': [9.6, 8.1, 6.8],
        'top5_count': [28, 22, 18],
        'top3_count': [18, 15, 12],
        'champion_count': [15, 12, 8]
    })
    
    return {
        'window_performance': window_results,
        'future_window_performance': future_window_results,
        'champions': champions,
        'ranking_frequency': ranking_frequency,
        'historical_analysis': historical_analysis,
        'future_analysis_enabled': True
    }

def test_data_flow():
    """测试数据流修复"""
    print("=" * 60)
    print("测试未来时间窗口数据流修复")
    print("=" * 60)
    
    # 创建测试数据
    test_data = create_test_data()
    
    print("📊 测试数据概览:")
    print(f"   • 历史时间窗口: {len(test_data['window_performance'])} 个")
    print(f"   • 未来时间窗口: {len(test_data['future_window_performance'])} 个")
    print(f"   • 多年份历史数据: {'是' if test_data['historical_analysis']['multi_year_rankings'] else '否'}")
    print(f"   • 未来分析启用: {test_data['future_analysis_enabled']}")
    
    # 创建输出目录
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 创建报告生成器
        report_generator = ReportGenerator(str(output_dir))
        
        # 生成HTML报告
        html_file = report_generator.generate_html_report(
            test_data,
            [],  # 空的图表列表
            "测试报告 - 未来时间窗口数据流修复验证"
        )
        
        if html_file and os.path.exists(html_file):
            print(f"\n✅ HTML报告生成成功: {os.path.basename(html_file)}")
            
            # 读取HTML文件内容进行验证
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 验证关键元素是否存在
            checks = [
                ('未来分析标题', '历史回望 + 未来前瞻' in html_content),
                ('历史时间窗口部分', '历史时间窗口分析（回望）' in html_content or 'multi_year_rankings' in html_content),
                ('未来时间窗口部分', '未来时间窗口分析（基于历史同期数据统计预测）' in html_content),
                ('未来窗口标签页', 'future-window-tab' in html_content),
                ('30天窗口', '30天窗口' in html_content),
                ('60天窗口', '60天窗口' in html_content),
                ('90天窗口', '90天窗口' in html_content),
                ('一致性表现板块', '一致性表现板块' in html_content),
                ('证券板块', '证券' in html_content),
                ('煤炭行业板块', '煤炭行业' in html_content),
                ('仪器仪表板块', '仪器仪表' in html_content),
                ('JavaScript函数', 'showFutureWindowTab' in html_content),
            ]
            
            print(f"\n🔍 HTML内容验证:")
            all_passed = True
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}: {'通过' if check_result else '失败'}")
                if not check_result:
                    all_passed = False
            
            if all_passed:
                print(f"\n🎉 所有验证通过！未来时间窗口数据流修复成功")
                print(f"📄 报告文件: {html_file}")
                return True
            else:
                print(f"\n⚠️ 部分验证失败，修复可能不完整")
                return False
                
        else:
            print(f"\n❌ HTML报告生成失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试未来时间窗口数据流修复...")
    
    success = test_data_flow()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ 未来时间窗口数据流修复测试通过！")
        print("修复要点:")
        print("1. 修复了HTML生成器中的逻辑错误")
        print("2. 确保多年份历史数据和未来时间窗口并存")
        print("3. 修复了主程序中的数据摘要显示")
        print("4. 未来时间窗口现在能正确显示在HTML中")
        print("=" * 60)
    else:
        print("\n❌ 修复测试失败，需要进一步检查")
