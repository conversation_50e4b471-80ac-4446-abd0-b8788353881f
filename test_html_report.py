#!/usr/bin/env python3
"""
测试HTML报告生成功能，验证未来时间窗口标签页是否正确显示
"""

import pandas as pd
import os
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from report_generator import ReportGenerator

def create_mock_data():
    """创建模拟数据用于测试"""
    
    # 模拟历史时间窗口数据
    window_results = {
        7: pd.DataFrame({
            'sector_name': ['板块A', '板块B', '板块C'],
            'sector_code': ['BK001', 'BK002', 'BK003'],
            'cumulative_return_pct': [5.2, 3.8, 2.1],
            'avg_daily_change_pct': [0.74, 0.54, 0.30],
            'volatility': [2.1, 1.8, 1.5]
        }),
        14: pd.DataFrame({
            'sector_name': ['板块B', '板块A', '板块D'],
            'sector_code': ['BK002', 'BK001', 'BK004'],
            'cumulative_return_pct': [8.1, 7.3, 4.2],
            'avg_daily_change_pct': [0.58, 0.52, 0.30],
            'volatility': [2.3, 2.0, 1.7]
        }),
        30: pd.DataFrame({
            'sector_name': ['板块C', '板块A', '板块B'],
            'sector_code': ['BK003', 'BK001', 'BK002'],
            'cumulative_return_pct': [12.5, 10.8, 9.2],
            'avg_daily_change_pct': [0.42, 0.36, 0.31],
            'volatility': [2.8, 2.5, 2.2]
        })
    }
    
    # 模拟未来时间窗口数据（包含一致性信息）
    future_window_results = {
        30: pd.DataFrame({
            'sector_name': ['板块A', '板块B', '板块C', '板块D'],
            'sector_code': ['BK001', 'BK002', 'BK003', 'BK004'],
            'avg_return_pct': [5.2, 3.8, 2.1, 1.5],
            'consistency_rate': [0.8, 0.6, 0.4, 0.7],
            'positive_years': [4, 3, 2, 3],
            'total_years': [5, 5, 5, 4],
            'historical_years': [[2020, 2021, 2022, 2023, 2024], [2020, 2021, 2023, 2024], [2021, 2023], [2021, 2022, 2024]]
        }),
        60: pd.DataFrame({
            'sector_name': ['板块B', '板块A', '板块E'],
            'sector_code': ['BK002', 'BK001', 'BK005'],
            'avg_return_pct': [7.1, 6.3, 3.2],
            'consistency_rate': [0.9, 0.7, 0.5],
            'positive_years': [4, 3, 2],
            'total_years': [4, 4, 4],
            'historical_years': [[2021, 2022, 2023, 2024], [2021, 2023, 2024], [2022, 2024]]
        }),
        90: pd.DataFrame({
            'sector_name': ['板块A', '板块F', '板块B'],
            'sector_code': ['BK001', 'BK006', 'BK002'],
            'avg_return_pct': [8.5, 5.7, 4.9],
            'consistency_rate': [0.8, 0.6, 0.5],
            'positive_years': [4, 3, 2],
            'total_years': [5, 5, 4],
            'historical_years': [[2020, 2021, 2022, 2024], [2021, 2022, 2024], [2023, 2024]]
        })
    }
    
    # 模拟其他数据
    champions = pd.DataFrame({
        'rank': [1, 2, 3],
        'sector_name': ['板块A', '板块B', '板块C'],
        'sector_code': ['BK001', 'BK002', 'BK003'],
        'champion_count': [15, 12, 8],
        'champion_frequency_pct': [3.2, 2.6, 1.7],
        'avg_champion_change_pct': [8.5, 7.2, 6.8]
    })
    
    ranking_frequency = pd.DataFrame({
        'rank': [1, 2, 3],
        'sector_name': ['板块A', '板块B', '板块C'],
        'sector_code': ['BK001', 'BK002', 'BK003'],
        'top10_count': [45, 38, 32],
        'top10_frequency_pct': [9.6, 8.1, 6.8],
        'top5_count': [28, 22, 18],
        'top3_count': [18, 15, 12],
        'champion_count': [15, 12, 8]
    })
    
    historical_analysis = {
        'multi_year_rankings': {},
        'consistency_analysis': {}
    }
    
    return {
        'window_performance': window_results,
        'future_window_performance': future_window_results,
        'champions': champions,
        'ranking_frequency': ranking_frequency,
        'historical_analysis': historical_analysis,
        'future_analysis_enabled': True
    }

def test_html_report_generation():
    """测试HTML报告生成功能"""
    print("=" * 60)
    print("测试HTML报告生成功能")
    print("=" * 60)
    
    # 创建输出目录
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 创建报告生成器
        report_generator = ReportGenerator(str(output_dir))
        
        # 创建模拟数据
        report_data = create_mock_data()
        
        print("📊 模拟数据创建完成:")
        print(f"   • 历史时间窗口: {len(report_data['window_performance'])} 个")
        print(f"   • 未来时间窗口: {len(report_data['future_window_performance'])} 个")
        print(f"   • 未来分析启用: {report_data['future_analysis_enabled']}")
        
        # 生成HTML报告
        html_file = report_generator.generate_html_report(
            report_data,
            [],  # 空的图表列表
            "测试HTML报告 - 未来时间窗口功能验证"
        )
        
        if html_file and os.path.exists(html_file):
            print(f"\n✅ HTML报告生成成功: {os.path.basename(html_file)}")
            
            # 读取HTML文件内容进行验证
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 验证关键元素是否存在
            checks = [
                ('历史时间窗口标签页', 'historical-window-tab' in html_content),
                ('未来时间窗口标签页', 'future-window-tab' in html_content),
                ('一致性表现板块', '一致性表现板块' in html_content),
                ('JavaScript函数', 'showHistoricalWindowTab' in html_content),
                ('JavaScript函数', 'showFutureWindowTab' in html_content),
                ('30天窗口标签', '30天窗口' in html_content),
                ('60天窗口标签', '60天窗口' in html_content),
                ('90天窗口标签', '90天窗口' in html_content),
                ('一致性率显示', 'consistency-rate' in html_content),
            ]
            
            print(f"\n🔍 HTML内容验证:")
            all_passed = True
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}: {'通过' if check_result else '失败'}")
                if not check_result:
                    all_passed = False
            
            if all_passed:
                print(f"\n🎉 所有验证通过！HTML报告功能正常")
                print(f"📄 报告文件: {html_file}")
                return True
            else:
                print(f"\n⚠️ 部分验证失败，请检查HTML生成逻辑")
                return False
                
        else:
            print(f"\n❌ HTML报告生成失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试HTML报告生成功能...")
    
    success = test_html_report_generation()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ HTML报告生成功能测试通过！")
        print("修复要点:")
        print("1. 添加了历史时间窗口独立标签页")
        print("2. 添加了未来时间窗口独立标签页")
        print("3. 实现了一致性表现板块的专门展示")
        print("4. 添加了相应的CSS样式和JavaScript函数")
        print("=" * 60)
    else:
        print("\n❌ HTML报告生成功能测试失败，需要进一步检查")
