#!/usr/bin/env python3
"""
测试年份收益率数据结构
"""

import sys
import os
sys.path.append('.')

from src.time_window_analyzer import TimeWindowAnalyzer
from src.data_loader import DataLoader
import pandas as pd

def test_year_returns_structure():
    print('=' * 60)
    print('测试年份收益率数据结构')
    print('=' * 60)
    
    # 创建一些测试数据
    test_data = []
    for year in [2020, 2021, 2022, 2023, 2024]:
        for day in range(1, 31):  # 30天数据
            test_data.append({
                'date': pd.Timestamp(f'{year}-07-{day:02d}'),
                'sector_code': 'BK0464',
                'sector_name': '石油行业',
                'change_pct': 1.5 + (day % 5) * 0.3  # 模拟收益率变化
            })
            test_data.append({
                'date': pd.Timestamp(f'{year}-07-{day:02d}'),
                'sector_code': 'BK0001',
                'sector_name': '证券',
                'change_pct': 1.2 + (day % 3) * 0.4  # 模拟收益率变化
            })

    df = pd.DataFrame(test_data)
    df = df.set_index(['date', 'sector_code'])
    
    print(f'创建测试数据: {len(df)} 条记录')
    print(f'数据日期范围: {df.index.get_level_values("date").min()} 到 {df.index.get_level_values("date").max()}')
    
    # 初始化分析器
    analyzer = TimeWindowAnalyzer(df)
    
    # 测试未来时间窗口分析
    result = analyzer.calculate_future_window_performance('2025-07-23', 30)
    
    if not result.empty:
        print('\n✅ 测试成功')
        print(f'分析结果: {len(result)} 个板块')
        print(f'数据列: {list(result.columns)}')
        
        # 检查第一个板块的数据
        first_sector = result.iloc[0]
        print(f'\n第一个板块详情:')
        print(f'  板块名称: {first_sector.get("sector_name", "N/A")}')
        print(f'  历史年份: {first_sector.get("historical_years", [])}')
        print(f'  年份收益率映射: {first_sector.get("year_returns_dict", {})}')
        
        # 检查是否包含year_returns_dict列
        if 'year_returns_dict' in result.columns:
            print('✅ year_returns_dict 列存在')
            
            # 显示所有板块的年份收益率映射
            print('\n所有板块的年份收益率映射:')
            for idx, row in result.iterrows():
                sector_name = row.get('sector_name', 'N/A')
                year_returns = row.get('year_returns_dict', {})
                print(f'  {sector_name}: {year_returns}')
        else:
            print('❌ year_returns_dict 列不存在')
            print(f'可用列: {list(result.columns)}')
    else:
        print('❌ 测试失败：结果为空')

if __name__ == '__main__':
    test_year_returns_structure()
